<?php

/**
 * Simple script to get the raw SQL query for "Shared With Me"
 * Run this in your Laravel project root: php get_shared_with_me_sql.php
 */

// Bootstrap Laravel
require_once 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\FileEntry;
use Illuminate\Support\Facades\DB;

// Set test user ID
$userId = 1; // Change this to your test user ID
$workspaceId = 0; // Set to workspace ID if needed

echo "Getting SQL query for Shared With Me (User ID: $userId)\n";
echo str_repeat("=", 60) . "\n\n";

// Enable query logging
DB::enableQueryLog();

try {
    // Build the exact same query as DriveEntriesLoader->sharedWithMe()
    $builder = FileEntry::where('public', false)
        ->orderBy(DB::raw('type = "folder"'), 'desc')
        ->with(['users', 'tags']);

    // Apply workspace filter if needed
    if ($workspaceId) {
        $builder->where('workspace_id', $workspaceId);
    }

    // Apply sharedWithUserOnly scope
    $builder->sharedWithUserOnly($userId);

    // Get SQL and bindings
    $sql = $builder->toSql();
    $bindings = $builder->getBindings();

    echo "LARAVEL GENERATED SQL:\n";
    echo str_repeat("-", 40) . "\n";
    echo $sql . "\n\n";
    
    echo "BINDINGS:\n";
    echo str_repeat("-", 40) . "\n";
    echo json_encode($bindings, JSON_PRETTY_PRINT) . "\n\n";

    // Replace placeholders with actual values for easier reading
    $finalSql = $sql;
    foreach ($bindings as $binding) {
        $finalSql = preg_replace('/\?/', is_string($binding) ? "'$binding'" : $binding, $finalSql, 1);
    }
    
    echo "FINAL SQL WITH VALUES:\n";
    echo str_repeat("-", 40) . "\n";
    echo $finalSql . "\n\n";

    // Also show the query log
    $queries = DB::getQueryLog();
    if (!empty($queries)) {
        echo "ACTUAL EXECUTED QUERIES:\n";
        echo str_repeat("-", 40) . "\n";
        foreach ($queries as $query) {
            echo "SQL: " . $query['query'] . "\n";
            echo "Bindings: " . json_encode($query['bindings']) . "\n";
            echo "Time: " . $query['time'] . "ms\n\n";
        }
    }

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Make sure you're running this from your Laravel project root.\n";
}

echo "\nRAW SQL QUERY YOU CAN USE DIRECTLY:\n";
echo str_repeat("=", 60) . "\n";
echo "
SELECT 
    fe.id,
    fe.name,
    fe.type,
    fe.file_size,
    fe.mime,
    fe.extension,
    fe.created_at,
    fe.updated_at,
    fe.parent_id,
    owner.first_name as owner_first_name,
    owner.last_name as owner_last_name,
    owner.email as owner_email,
    fem.permissions,
    fem.created_at as shared_at
FROM file_entries fe
INNER JOIN file_entry_models fem ON fe.id = fem.file_entry_id
INNER JOIN users owner ON fe.owner_id = owner.id
WHERE fe.public = 0
  AND fe.deleted_at IS NULL
  AND fem.model_id = $userId -- Current user ID
  AND fem.model_type = 'App\\\\Models\\\\User'
  AND fem.owner = 0 -- User is not the owner
  AND fe.owner_id != $userId -- Exclude files owned by user
ORDER BY 
  CASE WHEN fe.type = 'folder' THEN 0 ELSE 1 END DESC,
  fe.name ASC;
";

echo "\nTo use this query:\n";
echo "1. Replace $userId with the actual user ID\n";
echo "2. Execute in your database client or via Laravel DB::select()\n";
echo "3. The 'permissions' field contains JSON with user permissions\n\n";
