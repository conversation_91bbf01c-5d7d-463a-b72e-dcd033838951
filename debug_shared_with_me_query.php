<?php

/**
 * Debug script to get the raw SQL query for "Shared With Me" functionality
 * 
 * This script demonstrates how to extract the actual SQL query that <PERSON><PERSON> builds
 * for the sharedWithMe() method in DriveEntriesLoader
 */

require_once 'vendor/autoload.php';

use App\Models\FileEntry;
use Illuminate\Support\Facades\DB;

// Example usage - replace with actual user ID
$userId = 1; // Replace with actual user ID
$workspaceId = 0; // Set to actual workspace ID if needed

echo "=== SHARED WITH ME SQL QUERY DEBUG ===\n\n";

// Method 1: Using Laravel Query Builder with SQL logging
DB::enableQueryLog();

// Replicate the exact query from DriveEntriesLoader->sharedWithMe()
$builder = FileEntry::where('public', false)
    ->orderBy(DB::raw('type = "folder"'), 'desc')
    ->with(['users', 'tags']);

// Apply workspace filter if needed (from sharedWithMe method)
if ($workspaceId) {
    $builder->where('workspace_id', $workspaceId);
}

// Apply the sharedWithUserOnly scope
$builder->sharedWithUserOnly($userId);

// Get the SQL without executing
$sql = $builder->toSql();
$bindings = $builder->getBindings();

echo "1. LARAVEL QUERY BUILDER SQL:\n";
echo "SQL: " . $sql . "\n";
echo "Bindings: " . json_encode($bindings) . "\n\n";

// Method 2: Manual SQL construction based on the scope methods
echo "2. MANUAL RAW SQL QUERY:\n";

$rawSql = "
SELECT 
    fe.*,
    -- Include user relationship data
    u.first_name,
    u.last_name, 
    u.email,
    u.id as shared_by_user_id,
    u.image,
    fem.owner,
    fem.permissions,
    fem.created_at as shared_at
FROM file_entries fe
INNER JOIN file_entry_models fem ON fe.id = fem.file_entry_id
INNER JOIN users u ON fem.model_id = u.id AND fem.model_type = 'App\\\\Models\\\\User'
WHERE fe.public = 0
  AND fe.deleted_at IS NULL
  -- sharedWithUserOnly scope: get entries user has access to but doesn't own
  AND fe.id IN (
      SELECT file_entry_id 
      FROM file_entry_models 
      WHERE model_id = ? -- userId
        AND model_type = 'App\\\\Models\\\\User'
        AND owner = 0  -- user is not the owner
  )
  -- sharedWithUserOnly scope: exclude entries where parent is also shared with user
  AND NOT EXISTS (
      SELECT 1 
      FROM file_entries parent_fe
      INNER JOIN file_entry_models parent_fem ON parent_fe.id = parent_fem.file_entry_id
      WHERE parent_fe.id = fe.parent_id
        AND parent_fem.model_id = ? -- userId  
        AND parent_fem.model_type = 'App\\\\Models\\\\User'
        AND parent_fe.owner_id != ? -- userId (parent not owned by user)
  )
  -- Optional workspace filter
  " . ($workspaceId ? "AND fe.workspace_id = ? -- workspaceId" : "") . "
ORDER BY 
  CASE WHEN fe.type = 'folder' THEN 0 ELSE 1 END DESC,
  fe.name ASC
";

echo $rawSql . "\n\n";

// Method 3: Simplified version for most common use case
echo "3. SIMPLIFIED RAW SQL (Most Common Use Case):\n";

$simplifiedSql = "
SELECT 
    fe.id,
    fe.name,
    fe.type,
    fe.file_size,
    fe.mime,
    fe.extension,
    fe.created_at,
    fe.updated_at,
    fe.parent_id,
    owner.first_name as owner_first_name,
    owner.last_name as owner_last_name,
    owner.email as owner_email,
    fem.permissions,
    fem.created_at as shared_at
FROM file_entries fe
INNER JOIN file_entry_models fem ON fe.id = fem.file_entry_id
INNER JOIN users owner ON fe.owner_id = owner.id
WHERE fe.public = 0
  AND fe.deleted_at IS NULL
  AND fem.model_id = ? -- Replace with current user ID
  AND fem.model_type = 'App\\\\Models\\\\User'
  AND fem.owner = 0 -- User is not the owner, just has access
  AND fe.owner_id != ? -- Exclude files owned by current user
ORDER BY 
  CASE WHEN fe.type = 'folder' THEN 0 ELSE 1 END DESC,
  fe.name ASC
";

echo $simplifiedSql . "\n\n";

// Method 4: Show parameter binding example
echo "4. PARAMETER BINDING EXAMPLE:\n";
echo "For userId = $userId" . ($workspaceId ? " and workspaceId = $workspaceId" : "") . "\n\n";

$bindings = [$userId, $userId, $userId];
if ($workspaceId) {
    $bindings[] = $workspaceId;
}

echo "Bindings array: " . json_encode($bindings) . "\n\n";

// Method 5: Direct database execution example
echo "5. DIRECT DATABASE EXECUTION EXAMPLE:\n";
echo "
// Using Laravel DB facade
\$results = DB::select(\"
    SELECT fe.*, owner.first_name as owner_first_name, owner.last_name as owner_last_name
    FROM file_entries fe
    INNER JOIN file_entry_models fem ON fe.id = fem.file_entry_id  
    INNER JOIN users owner ON fe.owner_id = owner.id
    WHERE fe.public = 0
      AND fe.deleted_at IS NULL
      AND fem.model_id = ?
      AND fem.model_type = 'App\\\\\\\\Models\\\\\\\\User'
      AND fem.owner = 0
      AND fe.owner_id != ?
    ORDER BY CASE WHEN fe.type = 'folder' THEN 0 ELSE 1 END DESC, fe.name ASC
\", [$userId, $userId]);

// Using PDO directly
\$pdo = DB::getPdo();
\$stmt = \$pdo->prepare('SELECT ... same query as above ...');
\$stmt->execute([$userId, $userId]);
\$results = \$stmt->fetchAll(PDO::FETCH_ASSOC);
";

echo "\n=== END DEBUG ===\n";
